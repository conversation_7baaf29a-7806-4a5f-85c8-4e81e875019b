/*
 * Created Date: 5/12/2023 16:05:26
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 28th December 2023 15:30:33
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2023 GAPO
 */

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/usecase/model/base_input.dart';

part 'auth_check_email_request.g.dart';

@JsonSerializable()
class AuthCheckEmailRequest extends Equatable implements GPBaseInput {
  const AuthCheckEmailRequest(
    this.email,
    this.phoneNumber,
  );

  final String email;

  @J<PERSON><PERSON><PERSON>(name: "phone_number")
  final String phoneNumber;

  factory AuthCheckEmailRequest.fromJson(Map<String, dynamic> json) =>
      _$AuthCheckEmailRequestFromJson(json);
  Map<String, dynamic> toJson() => _$AuthCheckEmailRequestToJson(this);

  @override
  List<Object?> get props => [email, phoneNumber];
}
