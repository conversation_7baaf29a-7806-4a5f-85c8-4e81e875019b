<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/presentation/test/test_page.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/presentation/test">lib/presentation/test</a> - test_page.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">90.2&nbsp;%</td>
            <td class="headerCovTableEntry">41</td>
            <td class="headerCovTableEntry">37</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-30 17:08:58</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_bloc/flutter_bloc.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:get_it/get_it.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:gp_core/utils/log.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:gp_core_v2/base/bloc/common/common.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:gp_core_v2/base/exception/base/app_exception_wrapper.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'package:gp_core_v2/base/utils/extension/widget/bloc_extension.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span>              : import '../shared/shared.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import 'bloc/bloc.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import 'test_page_behavior.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : </span>
<span id="L13"><span class="lineNum">      13</span>              : class TestPage extends StatelessWidget with TestPageBehaviorMixin, TalkerMixin {</span>
<span id="L14"><span class="lineNum">      14</span> <span class="tlaGNC">          14 :   const TestPage({super.key});</span></span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L17"><span class="lineNum">      17</span>              :   Widget build(BuildContext context) {</span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaGNC">           2 :     return Scaffold(</span></span>
<span id="L19"><span class="lineNum">      19</span> <span class="tlaGNC">           2 :       appBar: AppBar(),</span></span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaGNC">           2 :       body: BlocProvider&lt;TestBloc&gt;(</span></span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaGNC">           6 :         create: (context) =&gt; GetIt.I&lt;TestBloc&gt;(),</span></span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           2 :         child: BlocBuilder&lt;TestBloc, TestState&gt;(</span></span>
<span id="L23"><span class="lineNum">      23</span>              :           // buildWhen: (previous, current) =&gt; previous != current,</span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaGNC">           2 :           builder: (BuildContext context, state) {</span></span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">           2 :             return SingleChildScrollView(</span></span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaGNC">           4 :               child: Column(children: [</span></span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           6 :                 Text(&quot;Current State: ${state.test}&quot;),</span></span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           2 :                 TextButton(</span></span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaGNC">           2 :                     onPressed: () =&gt; debugDumpRenderTree(),</span></span>
<span id="L30"><span class="lineNum">      30</span>              :                     // debugDumpApp(),</span>
<span id="L31"><span class="lineNum">      31</span>              :                     child: const Text(&quot;debugDumpRenderTree&quot;)),</span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           2 :                 TextButton(</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :                     onPressed: () =&gt; test(context),</span></span>
<span id="L34"><span class="lineNum">      34</span>              :                     child: const Text(&quot;TestEvent&quot;)),</span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaGNC">           2 :                 TextButton(</span></span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaGNC">           2 :                     onPressed: () =&gt; testCounter(context),</span></span>
<span id="L37"><span class="lineNum">      37</span>              :                     child: const Text(&quot;TestCounterEvent&quot;)),</span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">           2 :                 TextButton(</span></span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           2 :                   onPressed: () =&gt; authCheckEmailRequest(context),</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           2 :                   child: Row(</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           2 :                     children: [</span></span>
<span id="L42"><span class="lineNum">      42</span>              :                       const Text(&quot;email check&quot;),</span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           2 :                       Padding(</span></span>
<span id="L44"><span class="lineNum">      44</span>              :                         padding: const EdgeInsets.only(left: 16),</span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           2 :                         child: BlocBuilder&lt;CommonBloc, CommonState&gt;(</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           2 :                           builder: (BuildContext context, state) {</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">           4 :                             logDebug(&quot;commonState -&gt; $state&quot;);</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           2 :                             if (state.isLoading) {</span></span>
<span id="L49"><span class="lineNum">      49</span>              :                               return const CircularProgressIndicator();</span>
<span id="L50"><span class="lineNum">      50</span>              :                             }</span>
<span id="L51"><span class="lineNum">      51</span>              :                             return const Text(&quot;CurrentState: NoLoading&quot;);</span>
<span id="L52"><span class="lineNum">      52</span>              :                           },</span>
<span id="L53"><span class="lineNum">      53</span>              :                         ),</span>
<span id="L54"><span class="lineNum">      54</span>              :                       ),</span>
<span id="L55"><span class="lineNum">      55</span>              :                     ],</span>
<span id="L56"><span class="lineNum">      56</span>              :                   ),</span>
<span id="L57"><span class="lineNum">      57</span>              :                 ),</span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaGNC">           2 :                 TextButton(</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaGNC">           2 :                     onPressed: () =&gt; testError(context),</span></span>
<span id="L60"><span class="lineNum">      60</span>              :                     child: const Text(&quot;Handle error&quot;)),</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaGNC">           2 :                 const Text(&quot;handle common state &quot;).withBlocCommonBuilder(),</span></span>
<span id="L62"><span class="lineNum">      62</span>              :                 const Text(&quot;handle common state with overrideErrorString&quot;)</span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaGNC">           2 :                     .withBlocCommonBuilder(</span></span>
<span id="L64"><span class="lineNum">      64</span>              :                   overrideErrorString: &quot;overrideErrorString&quot;,</span>
<span id="L65"><span class="lineNum">      65</span>              :                 ),</span>
<span id="L66"><span class="lineNum">      66</span>              :                 const Text(&quot;handle common state with listener&quot;)</span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaGNC">           2 :                     .withBlocCommonListener(</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :                   listener: (_, state) {</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :                     logDebug(</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :                         'handle common state with listener: ${state.appExceptionWrapper?.errorMapped}');</span></span>
<span id="L71"><span class="lineNum">      71</span>              :                   },</span>
<span id="L72"><span class="lineNum">      72</span>              :                 ),</span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaGNC">           2 :                 TextButton(</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaGNC">           2 :                   onPressed: () =&gt; authCheckEmailRequestWithUseCase(context),</span></span>
<span id="L75"><span class="lineNum">      75</span>              :                   child: const Text('authCheckEmailRequestWithUseCase')</span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaGNC">           2 :                       .withBlocCommonBuilder(),</span></span>
<span id="L77"><span class="lineNum">      77</span>              :                 ),</span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaGNC">           2 :                 TextButton(</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaGNC">           2 :                   onPressed: () =&gt; navigateToLogin(context),</span></span>
<span id="L80"><span class="lineNum">      80</span>              :                   child: const Text(&quot;Go to login page&quot;),</span>
<span id="L81"><span class="lineNum">      81</span>              :                 )</span>
<span id="L82"><span class="lineNum">      82</span>              :               ]),</span>
<span id="L83"><span class="lineNum">      83</span>              :             );</span>
<span id="L84"><span class="lineNum">      84</span>              :           },</span>
<span id="L85"><span class="lineNum">      85</span>              :         ),</span>
<span id="L86"><span class="lineNum">      86</span>              :       ),</span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           2 :       floatingActionButton: FloatingActionButton(</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           1 :         onPressed: () {},</span></span>
<span id="L89"><span class="lineNum">      89</span>              :         child: const Icon(Icons.developer_mode),</span>
<span id="L90"><span class="lineNum">      90</span>              :       ),</span>
<span id="L91"><span class="lineNum">      91</span>              :     );</span>
<span id="L92"><span class="lineNum">      92</span>              :   }</span>
<span id="L93"><span class="lineNum">      93</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
