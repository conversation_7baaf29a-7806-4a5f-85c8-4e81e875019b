<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/domain/entity/test/assignee_work.entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/domain/entity/test">lib/domain/entity/test</a> - assignee_work.entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">2</td>
            <td class="headerCovTableEntry">2</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-30 17:08:58</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : // ignore_for_file: invalid_annotation_target</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : /*</span>
<span id="L4"><span class="lineNum">       4</span>              :  * Created Date: Wednesday, 3rd April 2024, 13:54:22</span>
<span id="L5"><span class="lineNum">       5</span>              :  * Author: Nguyen Manh Toan</span>
<span id="L6"><span class="lineNum">       6</span>              :  * -----</span>
<span id="L7"><span class="lineNum">       7</span>              :  * Last Modified: Wednesday, 3rd April 2024 13:57:46</span>
<span id="L8"><span class="lineNum">       8</span>              :  * Modified By: Nguyen Manh Toan</span>
<span id="L9"><span class="lineNum">       9</span>              :  * -----</span>
<span id="L10"><span class="lineNum">      10</span>              :  * Copyright (c) 2021 - 2024 GAPO</span>
<span id="L11"><span class="lineNum">      11</span>              :  */</span>
<span id="L12"><span class="lineNum">      12</span>              : </span>
<span id="L13"><span class="lineNum">      13</span>              : // ignore_for_file: public_member_api_docs</span>
<span id="L14"><span class="lineNum">      14</span>              : </span>
<span id="L15"><span class="lineNum">      15</span>              : import 'package:freezed_annotation/freezed_annotation.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span>              : part 'assignee_work.entity.freezed.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : part 'assignee_work.entity.g.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : </span>
<span id="L20"><span class="lineNum">      20</span>              : @Freezed(</span>
<span id="L21"><span class="lineNum">      21</span>              :   fromJson: true,</span>
<span id="L22"><span class="lineNum">      22</span>              :   toJson: true,</span>
<span id="L23"><span class="lineNum">      23</span>              : )</span>
<span id="L24"><span class="lineNum">      24</span>              : abstract class WorkEntity with _$WorkEntity {</span>
<span id="L25"><span class="lineNum">      25</span>              :   @JsonSerializable(explicitToJson: true)</span>
<span id="L26"><span class="lineNum">      26</span>              :   factory WorkEntity({</span>
<span id="L27"><span class="lineNum">      27</span>              :     String? company,</span>
<span id="L28"><span class="lineNum">      28</span>              :     String? department,</span>
<span id="L29"><span class="lineNum">      29</span>              :     String? title,</span>
<span id="L30"><span class="lineNum">      30</span>              :     String? departmentId,</span>
<span id="L31"><span class="lineNum">      31</span>              :     List&lt;dynamic&gt;? departments,</span>
<span id="L32"><span class="lineNum">      32</span>              :     List&lt;dynamic&gt;? departmentIds,</span>
<span id="L33"><span class="lineNum">      33</span>              :     String? roleId,</span>
<span id="L34"><span class="lineNum">      34</span>              :     int? privacy,</span>
<span id="L35"><span class="lineNum">      35</span>              :   }) = _WorkEntity;</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaGNC">           3 :   factory WorkEntity.fromJson(Map&lt;String, dynamic&gt; json) =&gt;</span></span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">           3 :       _$WorkEntityFromJson(json);</span></span>
<span id="L39"><span class="lineNum">      39</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
