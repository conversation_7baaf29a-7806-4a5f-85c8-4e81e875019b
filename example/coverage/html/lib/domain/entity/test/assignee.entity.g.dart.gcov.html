<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/domain/entity/test/assignee.entity.g.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/domain/entity/test">lib/domain/entity/test</a> - assignee.entity.g.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">37</td>
            <td class="headerCovTableEntry">37</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-30 17:08:58</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : // GENERATED CODE - DO NOT MODIFY BY HAND</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : part of 'assignee.entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : // **************************************************************************</span>
<span id="L6"><span class="lineNum">       6</span>              : // JsonSerializableGenerator</span>
<span id="L7"><span class="lineNum">       7</span>              : // **************************************************************************</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span> <span class="tlaGNC">           2 : _AssigneeEntity _$AssigneeEntityFromJson(Map&lt;String, dynamic&gt; json) =&gt;</span></span>
<span id="L10"><span class="lineNum">      10</span> <span class="tlaGNC">           2 :     _AssigneeEntity(</span></span>
<span id="L11"><span class="lineNum">      11</span> <span class="tlaGNC">           4 :       id: (json['id'] as num).toInt(),</span></span>
<span id="L12"><span class="lineNum">      12</span> <span class="tlaGNC">           2 :       displayName: json['displayName'] as String,</span></span>
<span id="L13"><span class="lineNum">      13</span> <span class="tlaGNC">           2 :       lang: json['lang'] as String?,</span></span>
<span id="L14"><span class="lineNum">      14</span> <span class="tlaGNC">           2 :       fullName: json['fullName'] as String?,</span></span>
<span id="L15"><span class="lineNum">      15</span> <span class="tlaGNC">           2 :       cover: json['cover'] as String?,</span></span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaGNC">           2 :       avatar: json['avatar'] as String?,</span></span>
<span id="L17"><span class="lineNum">      17</span> <span class="tlaGNC">           2 :       email: json['email'] as String?,</span></span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaGNC">           2 :       linkProfile: json['linkProfile'] as String?,</span></span>
<span id="L19"><span class="lineNum">      19</span> <span class="tlaGNC">           2 :       info: json['info'] == null</span></span>
<span id="L20"><span class="lineNum">      20</span>              :           ? null</span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaGNC">           2 :           : InfoEntity.fromJson(json['info'] as Map&lt;String, dynamic&gt;),</span></span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           3 :       workspaceAccount: (json['workspaceAccount'] as num?)?.toInt(),</span></span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaGNC">           2 :       workspaceId: json['workspaceId'] as String?,</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaGNC">           2 :       phoneNumber: json['phoneNumber'] as String?,</span></span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">           2 :       avatarThumbPattern: json['avatarThumbPattern'] as String?,</span></span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaGNC">           2 :       coverThumbPattern: json['coverThumbPattern'] as String?,</span></span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           2 :       userDepartment: json['userDepartment'] as String?,</span></span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           2 :       userRole: json['userRole'] as String?,</span></span>
<span id="L29"><span class="lineNum">      29</span>              :     );</span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaGNC">           2 : Map&lt;String, dynamic&gt; _$AssigneeEntityToJson(_AssigneeEntity instance) =&gt;</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           2 :     &lt;String, dynamic&gt;{</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaGNC">           2 :       'id': instance.id,</span></span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           2 :       'displayName': instance.displayName,</span></span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaGNC">           2 :       'lang': instance.lang,</span></span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaGNC">           2 :       'fullName': instance.fullName,</span></span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaGNC">           2 :       'cover': instance.cover,</span></span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">           2 :       'avatar': instance.avatar,</span></span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           2 :       'email': instance.email,</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           2 :       'linkProfile': instance.linkProfile,</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           3 :       'info': instance.info?.toJson(),</span></span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">           2 :       'workspaceAccount': instance.workspaceAccount,</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           2 :       'workspaceId': instance.workspaceId,</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           2 :       'phoneNumber': instance.phoneNumber,</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           2 :       'avatarThumbPattern': instance.avatarThumbPattern,</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           2 :       'coverThumbPattern': instance.coverThumbPattern,</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">           2 :       'userDepartment': instance.userDepartment,</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           2 :       'userRole': instance.userRole,</span></span>
<span id="L49"><span class="lineNum">      49</span>              :     };</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
