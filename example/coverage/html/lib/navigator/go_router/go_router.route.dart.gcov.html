<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/navigator/go_router/go_router.route.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/navigator/go_router">lib/navigator/go_router</a> - go_router.route.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">20.0&nbsp;%</td>
            <td class="headerCovTableEntry">15</td>
            <td class="headerCovTableEntry">3</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-30 17:08:58</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : /*</span>
<span id="L2"><span class="lineNum">       2</span>              :  * Created Date: Wednesday, 22nd January 2025, 10:35:15</span>
<span id="L3"><span class="lineNum">       3</span>              :  * Author: Nguyen Manh Toan</span>
<span id="L4"><span class="lineNum">       4</span>              :  * -----</span>
<span id="L5"><span class="lineNum">       5</span>              :  * Last Modified: Saturday, 25th January 2025 11:07:27</span>
<span id="L6"><span class="lineNum">       6</span>              :  * Modified By: Nguyen Manh Toan</span>
<span id="L7"><span class="lineNum">       7</span>              :  * -----</span>
<span id="L8"><span class="lineNum">       8</span>              :  * Copyright (c) 2021 - 2025 GAPO</span>
<span id="L9"><span class="lineNum">       9</span>              :  */</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : import 'package:example/domain/entity/test.entity.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import 'package:flutter/material.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import 'package:go_router/go_router.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : </span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../domain/entity/test/assignee.entity.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../../presentation/presentation.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../model/example_app_info_route.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              : part 'go_router.route.g.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span>              : /// Route hiển thị màn hình Test,</span>
<span id="L22"><span class="lineNum">      22</span>              : @TypedGoRoute&lt;ExampleTestRouteData&gt;(</span>
<span id="L23"><span class="lineNum">      23</span>              :   path: kExampleInitial,</span>
<span id="L24"><span class="lineNum">      24</span>              :   name: 'ExampleTestPage',</span>
<span id="L25"><span class="lineNum">      25</span>              : )</span>
<span id="L26"><span class="lineNum">      26</span>              : class ExampleTestRouteData extends GoRouteData with _$ExampleTestRouteData {</span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">          13 :   const ExampleTestRouteData();</span></span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L30"><span class="lineNum">      30</span>              :   Widget build(BuildContext context, GoRouterState state) {</span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :     return TestPage();</span></span>
<span id="L32"><span class="lineNum">      32</span>              :   }</span>
<span id="L33"><span class="lineNum">      33</span>              : }</span>
<span id="L34"><span class="lineNum">      34</span>              : </span>
<span id="L35"><span class="lineNum">      35</span>              : /// Route hiển thị màn hình đăng nhập,</span>
<span id="L36"><span class="lineNum">      36</span>              : @TypedGoRoute&lt;ExampleLoginRouteData&gt;(</span>
<span id="L37"><span class="lineNum">      37</span>              :   path: kExampleLogin,</span>
<span id="L38"><span class="lineNum">      38</span>              :   name: 'ExampleLoginPage',</span>
<span id="L39"><span class="lineNum">      39</span>              : )</span>
<span id="L40"><span class="lineNum">      40</span>              : class ExampleLoginRouteData extends GoRouteData with _$ExampleLoginRouteData {</span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">          13 :   const ExampleLoginRouteData();</span></span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L44"><span class="lineNum">      44</span>              :   Widget build(BuildContext context, GoRouterState state) {</span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :     return ExampleLoginPage();</span></span>
<span id="L46"><span class="lineNum">      46</span>              :   }</span>
<span id="L47"><span class="lineNum">      47</span>              : }</span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span>              : /// Route hiển thị màn hình Home</span>
<span id="L50"><span class="lineNum">      50</span>              : @TypedGoRoute&lt;ExampleHomeRouteData&gt;(</span>
<span id="L51"><span class="lineNum">      51</span>              :   path: kExampleHome,</span>
<span id="L52"><span class="lineNum">      52</span>              :   name: 'ExampleHomePage',</span>
<span id="L53"><span class="lineNum">      53</span>              : )</span>
<span id="L54"><span class="lineNum">      54</span>              : class ExampleHomeRouteData extends GoRouteData with _$ExampleHomeRouteData {</span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaGNC">          13 :   const ExampleHomeRouteData();</span></span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L58"><span class="lineNum">      58</span>              :   Widget build(BuildContext context, GoRouterState state) {</span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :     return ExampleHomePage();</span></span>
<span id="L60"><span class="lineNum">      60</span>              :   }</span>
<span id="L61"><span class="lineNum">      61</span>              : }</span>
<span id="L62"><span class="lineNum">      62</span>              : </span>
<span id="L63"><span class="lineNum">      63</span>              : /// Route hiển thị màn hình User</span>
<span id="L64"><span class="lineNum">      64</span>              : @TypedGoRoute&lt;ExampleUserRouteData&gt;(</span>
<span id="L65"><span class="lineNum">      65</span>              :   path: kExampleUserDetails,</span>
<span id="L66"><span class="lineNum">      66</span>              :   name: 'ExampleUserPage',</span>
<span id="L67"><span class="lineNum">      67</span>              : )</span>
<span id="L68"><span class="lineNum">      68</span>              : class ExampleUserRouteData extends GoRouteData with _$ExampleUserRouteData {</span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :   const ExampleUserRouteData({</span></span>
<span id="L70"><span class="lineNum">      70</span>              :     required this.$extra,</span>
<span id="L71"><span class="lineNum">      71</span>              :   });</span>
<span id="L72"><span class="lineNum">      72</span>              : </span>
<span id="L73"><span class="lineNum">      73</span>              :   final User $extra;</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L76"><span class="lineNum">      76</span>              :   Widget build(BuildContext context, GoRouterState state) {</span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :     return ExampleUserPage(user: $extra);</span></span>
<span id="L78"><span class="lineNum">      78</span>              :   }</span>
<span id="L79"><span class="lineNum">      79</span>              : }</span>
<span id="L80"><span class="lineNum">      80</span>              : </span>
<span id="L81"><span class="lineNum">      81</span>              : /// Route hiển thị màn hình Assignee</span>
<span id="L82"><span class="lineNum">      82</span>              : @TypedGoRoute&lt;ExampleAssigneeRouteData&gt;(</span>
<span id="L83"><span class="lineNum">      83</span>              :   path: kExampleAssigneeDetails,</span>
<span id="L84"><span class="lineNum">      84</span>              :   name: 'ExampleAssigneePage',</span>
<span id="L85"><span class="lineNum">      85</span>              : )</span>
<span id="L86"><span class="lineNum">      86</span>              : class ExampleAssigneeRouteData extends GoRouteData</span>
<span id="L87"><span class="lineNum">      87</span>              :     with _$ExampleAssigneeRouteData {</span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :   const ExampleAssigneeRouteData({</span></span>
<span id="L89"><span class="lineNum">      89</span>              :     required this.$extra,</span>
<span id="L90"><span class="lineNum">      90</span>              :   });</span>
<span id="L91"><span class="lineNum">      91</span>              : </span>
<span id="L92"><span class="lineNum">      92</span>              :   final AssigneeEntity $extra;</span>
<span id="L93"><span class="lineNum">      93</span>              : </span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L95"><span class="lineNum">      95</span>              :   Widget build(BuildContext context, GoRouterState state) {</span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :     return ExampleAssigneePage(assigneeEntity: $extra);</span></span>
<span id="L97"><span class="lineNum">      97</span>              :   }</span>
<span id="L98"><span class="lineNum">      98</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
