import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/bloc/common/common.dart';
import 'package:example/presentation/test/bloc/test_bloc.dart';
import 'package:example/presentation/test/bloc/test_event.dart';
import 'package:example/presentation/test/bloc/test_state.dart';
import 'package:example/data/model/auth/response/auth/auth.dart';
import 'package:gp_core_v2/base/usecase/model/response/api_response_v2.dart';
import 'package:example/mapper/entity/mapper.dart';


import '../../helpers/test_helper.mocks.dart';
import '../../helpers/test_constants.dart';

void main() {
  group('TestBloc', () {
    late MockAuthCheckMailUseCase mockAuthCheckMailUseCase;
    late TestBloc testBloc;
    late CommonBloc commonBloc;

    setUp(() {
      mockAuthCheckMailUseCase = MockAuthCheckMailUseCase();
      commonBloc = CommonBloc();

      // Stub the use case execute method
      when(mockAuthCheckMailUseCase.execute(any)).thenAnswer(
        (_) async => ApiResponseV2<AuthCheckMailResponse>(
          status: 'success',
          data: AuthCheckMailResponse(
            userId: 123,
            newDomain: true,
            salt: 'test_salt',
          ),
        ),
      );

      // Register CommonBloc in GetIt
      if (GetIt.I.isRegistered<CommonBloc>(instanceName: 'kCommonBloc')) {
        GetIt.I.unregister<CommonBloc>(instanceName: 'kCommonBloc');
      }
      GetIt.I.registerSingleton<CommonBloc>(commonBloc, instanceName: 'kCommonBloc');

      // Register GPMapper in GetIt
      if (GetIt.I.isRegistered<GPMapper>(instanceName: 'kGPMapper')) {
        GetIt.I.unregister<GPMapper>(instanceName: 'kGPMapper');
      }
      GetIt.I.registerSingleton<GPMapper>(GPMapper(), instanceName: 'kGPMapper');

      testBloc = TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase);
    });

    tearDown(() {
      testBloc.close();
      commonBloc.close();
      if (GetIt.I.isRegistered<CommonBloc>(instanceName: 'kCommonBloc')) {
        GetIt.I.unregister<CommonBloc>(instanceName: 'kCommonBloc');
      }
      if (GetIt.I.isRegistered<GPMapper>(instanceName: 'kGPMapper')) {
        GetIt.I.unregister<GPMapper>(instanceName: 'kGPMapper');
      }
    });

    test('initial state should be TestState with "test"', () {
      expect(testBloc.state, const TestState("test"));
    });

    group('TestEvent', () {
      blocTest<TestBloc, TestState>(
        'should throw exception when TestEvent is added',
        build: () => TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase),
        act: (bloc) => bloc.add(const TestEvent()),
        errors: () => [isA<Exception>()],
      );
    });

    group('TestCounterEvent', () {
      blocTest<TestBloc, TestState>(
        'should emit new state with counter value when TestCounterEvent is added',
        build: () => TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase),
        act: (bloc) => bloc.add(const TestCounterEvent(100)),
        expect: () => [const TestState("100")],
      );

      blocTest<TestBloc, TestState>(
        'should handle different counter values',
        build: () => TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase),
        act: (bloc) {
          bloc.add(const TestCounterEvent(50));
          bloc.add(const TestCounterEvent(200));
        },
        expect: () => [
          const TestState("50"),
          const TestState("200"),
        ],
      );

      blocTest<TestBloc, TestState>(
        'should ignore duplicate counter events due to distinct transformer',
        build: () => TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase),
        act: (bloc) {
          bloc.add(const TestCounterEvent(100));
          bloc.add(const TestCounterEvent(100)); // Should be ignored
          bloc.add(const TestCounterEvent(200));
        },
        expect: () => [
          const TestState("100"),
          const TestState("200"),
        ],
      );

      blocTest<TestBloc, TestState>(
        'should handle zero and negative counter values',
        build: () => TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase),
        act: (bloc) {
          bloc.add(const TestCounterEvent(0));
          bloc.add(const TestCounterEvent(-1));
        },
        expect: () => [
          const TestState("0"),
          const TestState("-1"),
        ],
      );
    });

    group('AuthEmailCheck', () {
      blocTest<TestBloc, TestState>(
        'should emit success state when auth check succeeds',
        build: () {
          // Mock the execute method instead of buildUseCase since _onAuthEmailCheck calls execute
          when(mockAuthCheckMailUseCase.execute(any))
              .thenAnswer((_) async => TestConstants.testSuccessApiResponse);
          return TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase);
        },
        act: (bloc) => bloc.add(AuthEmailCheck(TestConstants.testAuthCheckEmailRequest)),
        expect: () => [const TestState("success")],
        wait: const Duration(seconds: 2), // Wait for Future.delayed(Duration(seconds: 1)) in _onAuthEmailCheck
        verify: (_) {
          verify(mockAuthCheckMailUseCase.execute(any)).called(1);
        },
      );

      blocTest<TestBloc, TestState>(
        'should handle auth check failure',
        build: () {
          // Mock the execute method and it should still emit success since _onAuthEmailCheck always emits success
          when(mockAuthCheckMailUseCase.execute(any))
              .thenAnswer((_) async => TestConstants.testErrorApiResponse);
          return TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase);
        },
        act: (bloc) => bloc.add(AuthEmailCheck(TestConstants.testAuthCheckEmailRequest)),
        expect: () => [const TestState("success")], // Always success because _onAuthEmailCheck doesn't check response status
        wait: const Duration(seconds: 2), // Wait for Future.delayed(Duration(seconds: 1)) in _onAuthEmailCheck
        verify: (_) {
          verify(mockAuthCheckMailUseCase.execute(any)).called(1);
        },
      );

      blocTest<TestBloc, TestState>(
        'should handle use case exception',
        build: () {
          // Mock the execute method to throw exception
          when(mockAuthCheckMailUseCase.execute(any))
              .thenThrow(Exception('Use case error'));
          return TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase);
        },
        act: (bloc) => bloc.add(AuthEmailCheck(TestConstants.testAuthCheckEmailRequest)),
        expect: () => <TestState>[], // No state emitted because exception is thrown before emit
        errors: () => [isA<Exception>()], // Exception is thrown and not caught
        verify: (_) {
          verify(mockAuthCheckMailUseCase.execute(any)).called(1);
        },
      );

      blocTest<TestBloc, TestState>(
        'should ignore duplicate auth check events due to distinct transformer',
        build: () {
          when(mockAuthCheckMailUseCase.execute(any))
              .thenAnswer((_) async => TestConstants.testSuccessApiResponse);
          return TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase);
        },
        act: (bloc) {
          final request = TestConstants.testAuthCheckEmailRequest;
          bloc.add(AuthEmailCheck(request));
          bloc.add(AuthEmailCheck(request)); // Should be ignored due to distinct
        },
        expect: () => [const TestState("success")],
        wait: const Duration(seconds: 2), // Wait for Future.delayed(Duration(seconds: 1)) in _onAuthEmailCheck
        verify: (_) {
          // Should only be called once due to distinct transformer
          verify(mockAuthCheckMailUseCase.execute(any)).called(1);
        },
      );
    });

    group('AuthEmailCheckWithRunCatching', () {
      blocTest<TestBloc, TestState>(
        'should emit success state when auth check with run catching succeeds',
        build: () {
          // Mock the execute method since _onAuthEmailCheckWithRunCatching calls execute
          when(mockAuthCheckMailUseCase.execute(any))
              .thenAnswer((_) async => TestConstants.testSuccessApiResponse);
          return TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase);
        },
        act: (bloc) => bloc.add(AuthEmailCheckWithRunCatching(TestConstants.testAuthCheckEmailRequest)),
        expect: () => [const TestState("success")],
        wait: const Duration(seconds: 3), // Wait for runCatching to complete
        verify: (_) {
          verify(mockAuthCheckMailUseCase.execute(any)).called(1);
        },
      );

      blocTest<TestBloc, TestState>(
        'should handle auth check with run catching failure gracefully',
        build: () {
          // Mock the execute method to throw exception
          when(mockAuthCheckMailUseCase.execute(any))
              .thenThrow(Exception('Network error'));
          return TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase);
        },
        act: (bloc) => bloc.add(AuthEmailCheckWithRunCatching(TestConstants.testAuthCheckEmailRequest)),
        expect: () => [const TestState("success")], // runCatching handles error and calls doOnCompleted
        wait: const Duration(seconds: 3), // Wait for runCatching to complete
        verify: (_) {
          verify(mockAuthCheckMailUseCase.execute(any)).called(1);
        },
      );
    });

    group('TestError', () {
      blocTest<TestBloc, TestState>(
        'should handle error with run catching and emit success state',
        build: () => TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase),
        act: (bloc) => bloc.add(const TestError()),
        expect: () => [const TestState("success")],
        wait: const Duration(seconds: 5), // Wait longer for runCatching with retries and delays
      );
    });

    group('TestErrorWithCatching', () {
      blocTest<TestBloc, TestState>(
        'should handle error with catching',
        build: () => TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase),
        act: (bloc) => bloc.add(const TestErrorWithCatching()),
        expect: () => [const TestState("success")],
        wait: const Duration(seconds: 5), // Wait longer for runCatching with retries
      );
    });

    group('Event Equality', () {
      test('TestEvent should be equal to another TestEvent', () {
        const event1 = TestEvent();
        const event2 = TestEvent();
        expect(event1, equals(event2));
      });

      test('TestCounterEvent should be equal when counter values are same', () {
        const event1 = TestCounterEvent(100);
        const event2 = TestCounterEvent(100);
        expect(event1, equals(event2));
      });

      test('TestCounterEvent should not be equal when counter values are different', () {
        const event1 = TestCounterEvent(100);
        const event2 = TestCounterEvent(200);
        expect(event1, isNot(equals(event2)));
      });

      test('AuthEmailCheck should be equal when requests are same', () {
        final request = TestConstants.testAuthCheckEmailRequest;
        final event1 = AuthEmailCheck(request);
        final event2 = AuthEmailCheck(request);
        expect(event1, equals(event2));
      });
    });

    group('State Equality', () {
      test('TestState should be equal when test values are same', () {
        const state1 = TestState("test");
        const state2 = TestState("test");
        expect(state1, equals(state2));
      });

      test('TestState should not be equal when test values are different', () {
        const state1 = TestState("test1");
        const state2 = TestState("test2");
        expect(state1, isNot(equals(state2)));
      });

      test('TestState props should contain test value', () {
        const state = TestState("test");
        expect(state.props, contains("test"));
      });
    });

    group('Bloc Lifecycle', () {
      test('should close properly without errors', () async {
        final bloc = TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase);
        await bloc.close();
        expect(bloc.isClosed, isTrue);
      });

      test('should not emit states after being closed', () async {
        final bloc = TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase);
        await bloc.close();
        
        expect(() => bloc.add(const TestCounterEvent(100)), throwsStateError);
      });
    });
  });
}
