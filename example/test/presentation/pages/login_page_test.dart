import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:example/presentation/login/login_page.dart';
import 'package:example/navigator/model/example_app_info_route.dart';
import 'package:gp_core_v2/base/navigator/base_app_navigator/app_navigator.dart';
import 'package:mockito/mockito.dart';

import '../../helpers/test_helper.mocks.dart';

void main() {
  group('ExampleLoginPage', () {
    late MockGPAppNavigator<ExampleAppInfoRoute> mockAppNavigator;

    setUp(() {
      mockAppNavigator = MockGPAppNavigator<ExampleAppInfoRoute>();

      // Stub the popAndPush method
      when(mockAppNavigator.popAndPush(any, any,
              result: anyNamed('result'),
              useRootNavigator: anyNamed('useRootNavigator'),
              arguments: anyNamed('arguments')))
          .thenAnswer((_) async => null);
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: RepositoryProvider<GPAppNavigator<ExampleAppInfoRoute>>(
          create: (context) => mockAppNavigator,
          child: const ExampleLoginPage(),
        ),
      );
    }

    testWidgets('should display login page with correct elements', (tester) async {
      await tester.pumpWidget(createTestWidget());

      // Verify AppBar
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.text('Login'), findsNWidgets(2)); // AppBar title + button text

      // Verify form fields
      expect(find.byType(TextFormField), findsNWidgets(2));
      
      // Verify email and password labels
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);

      // Verify login button
      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.text('Login'), findsNWidgets(2)); // AppBar title + button text
    });

    testWidgets('should have correct layout structure', (tester) async {
      await tester.pumpWidget(createTestWidget());

      // Verify Scaffold
      expect(find.byType(Scaffold), findsOneWidget);

      // Verify SafeArea
      expect(find.byType(SafeArea), findsAtLeastNWidgets(1));

      // Verify Padding
      expect(find.byType(Padding), findsAtLeastNWidgets(1));

      // Verify SingleChildScrollView
      expect(find.byType(SingleChildScrollView), findsOneWidget);

      // Verify Column
      expect(find.byType(Column), findsOneWidget);

      // Verify SizedBox for spacing
      expect(find.byType(SizedBox), findsAtLeastNWidgets(1));
    });

    testWidgets('should handle text input correctly', (tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find text fields by type and enter text
      final textFields = find.byType(TextFormField);
      expect(textFields, findsNWidgets(2));

      // Enter text in first field (email)
      await tester.enterText(textFields.first, '<EMAIL>');
      expect(find.text('<EMAIL>'), findsOneWidget);

      // Enter text in second field (password)
      await tester.enterText(textFields.last, 'password123');
      expect(find.text('password123'), findsOneWidget);
    });

    testWidgets('should handle login button tap', (tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find login button
      final loginButton = find.byType(ElevatedButton);
      expect(loginButton, findsOneWidget);

      // Tap login button
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      // Verify that popAndPush was called
      verify(mockAppNavigator.popAndPush(any, any,
              result: anyNamed('result'),
              useRootNavigator: anyNamed('useRootNavigator'),
              arguments: anyNamed('arguments')))
          .called(1);
    });

    testWidgets('should have correct padding values', (tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find the main padding widget
      final paddingWidget = find.byWidgetPredicate(
        (widget) => widget is Padding && 
                   widget.padding == const EdgeInsets.fromLTRB(16, 100, 16, 16),
      );
      expect(paddingWidget, findsOneWidget);
    });

    testWidgets('should have correct spacing between elements', (tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find SizedBox with height 16
      final spacingWidget = find.byWidgetPredicate(
        (widget) => widget is SizedBox && widget.height == 16,
      );
      expect(spacingWidget, findsOneWidget);
    });

    group('Widget Properties', () {
      testWidgets('should have correct AppBar properties', (tester) async {
        await tester.pumpWidget(createTestWidget());

        final appBar = tester.widget<AppBar>(find.byType(AppBar));
        expect(appBar.title, isA<Text>());
        
        final titleText = appBar.title as Text;
        expect(titleText.data, 'Login');
      });

      testWidgets('should have correct TextFormField properties', (tester) async {
        await tester.pumpWidget(createTestWidget());

        final textFields = tester.widgetList<TextFormField>(find.byType(TextFormField));
        expect(textFields.length, 2);

        // Verify text fields exist
        expect(find.byType(TextFormField), findsNWidgets(2));

        // Verify labels exist
        expect(find.text('Email'), findsOneWidget);
        expect(find.text('Password'), findsOneWidget);
      });

      testWidgets('should have correct ElevatedButton properties', (tester) async {
        await tester.pumpWidget(createTestWidget());

        final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
        expect(button.child, isA<Text>());
        
        final buttonText = button.child as Text;
        expect(buttonText.data, 'Login');
        expect(button.onPressed, isNotNull);
      });
    });

    group('Accessibility', () {
      testWidgets('should have accessible elements', (tester) async {
        await tester.pumpWidget(createTestWidget());

        // Verify that form fields are accessible
        expect(find.byType(TextFormField), findsNWidgets(2));
        expect(find.byType(ElevatedButton), findsOneWidget);

        // Verify semantic labels exist
        expect(find.text('Email'), findsOneWidget);
        expect(find.text('Password'), findsOneWidget);
        expect(find.text('Login'), findsNWidgets(2));
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle empty input fields', (tester) async {
        await tester.pumpWidget(createTestWidget());

        // Tap login button without entering any text
        final loginButton = find.byType(ElevatedButton);

        // Tap login button
        await tester.tap(loginButton);
        await tester.pumpAndSettle();

        // Verify that popAndPush was called even with empty fields
        verify(mockAppNavigator.popAndPush(any, any,
                result: anyNamed('result'),
                useRootNavigator: anyNamed('useRootNavigator'),
                arguments: anyNamed('arguments')))
            .called(1);
      });

      testWidgets('should handle very long text input', (tester) async {
        await tester.pumpWidget(createTestWidget());

        final longText = 'a' * 1000;

        // Enter very long text in first field
        final textFields = find.byType(TextFormField);
        await tester.enterText(textFields.first, longText);

        // Should handle long text without crashing
        expect(find.byType(ExampleLoginPage), findsOneWidget);
      });

      testWidgets('should handle special characters in input', (tester) async {
        await tester.pumpWidget(createTestWidget());

        const specialText = '!@#\$%^&*()_+-=[]{}|;:,.<>?';

        // Enter special characters in second field
        final textFields = find.byType(TextFormField);
        await tester.enterText(textFields.last, specialText);

        // Should handle special characters without crashing
        expect(find.text(specialText), findsOneWidget);
      });
    });

    group('Performance', () {
      testWidgets('should build efficiently', (tester) async {
        final stopwatch = Stopwatch()..start();
        
        await tester.pumpWidget(createTestWidget());
        
        stopwatch.stop();
        
        // Should build within reasonable time (adjust as needed)
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });

      testWidgets('should handle multiple rebuilds efficiently', (tester) async {
        await tester.pumpWidget(createTestWidget());
        
        final stopwatch = Stopwatch()..start();
        
        // Trigger multiple rebuilds
        for (int i = 0; i < 10; i++) {
          await tester.pump();
        }
        
        stopwatch.stop();
        
        // Should handle rebuilds efficiently
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
      });
    });

    group('Widget State', () {
      testWidgets('should maintain state during interaction', (tester) async {
        await tester.pumpWidget(createTestWidget());

        // Enter text in fields
        final textFields = find.byType(TextFormField);
        await tester.enterText(textFields.first, '<EMAIL>');
        await tester.enterText(textFields.last, 'password123');

        // Pump to ensure state is maintained
        await tester.pump();

        // Verify text is still there
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text('password123'), findsOneWidget);
      });
    });
  });
}
